'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  History,
  Download,
  Trash2,
  ChevronDown,
  ChevronUp,
  Clock,
  Image as ImageIcon,
  AlertCircle
} from 'lucide-react';
import Image from 'next/image';
import { useStoredImages, useStorageStats } from '@/hooks/useImageStorage';
import { StoredImage } from '@/lib/imageStorage';
import { toast } from 'sonner';

interface RecentImagesProps {
  onImageSelect?: (image: StoredImage) => void;
  className?: string;
}

export function RecentImages({ onImageSelect, className = '' }: RecentImagesProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { images, loading, error, clearImages, refreshImages } = useStoredImages();
  const { stats } = useStorageStats();

  const handleImageClick = (image: StoredImage) => {
    if (onImageSelect) {
      onImageSelect(image);
      toast.success(`Restored image: ${image.originalFileName}`);
    }
  };

  const handleDownload = (image: StoredImage, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the image click
    
    try {
      const link = document.createElement('a');
      link.href = image.processedImageUrl;
      link.download = `tears-of-the-left-${image.originalFileName}-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast.success('Image downloaded successfully');
    } catch (err) {
      console.error('❌ Download failed:', err);
      toast.error('Failed to download image');
    }
  };

  const handleClearAll = async () => {
    try {
      await clearImages();
      toast.success('All stored images cleared');
    } catch (err) {
      console.error('❌ Clear failed:', err);
      toast.error('Failed to clear images');
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (error) {
    return (
      <Card className={`border-destructive/50 bg-destructive/10 ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">Storage Error: {error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`border-accent/30 bg-secondary/5 ${className}`}>
      <CardHeader className="py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <History className="h-5 w-5 text-accent" />
            <CardTitle className="text-lg">Recent Images</CardTitle>
            <Badge variant="secondary" className="text-xs">
              {stats.count}/{5}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-8 w-8 p-0"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-accent"></div>
              <span className="ml-2 text-sm text-muted-foreground">Loading images...</span>
            </div>
          ) : images.length === 0 ? (
            <div className="text-center py-8">
              <ImageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
              <p className="text-sm text-muted-foreground">No images stored yet</p>
              <p className="text-xs text-muted-foreground mt-1">
                Process an image to see it here
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Images Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                {images.map((image) => (
                  <div
                    key={image.id}
                    className="group relative bg-accent/5 rounded-lg border border-accent/20 overflow-hidden cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-[1.02]"
                    onClick={() => handleImageClick(image)}
                  >
                    {/* Thumbnail */}
                    <div className="aspect-square relative">
                      <Image
                        src={image.thumbnail || image.processedImageUrl}
                        alt={image.originalFileName}
                        fill
                        className="object-cover"
                        unoptimized={true}
                      />
                      
                      {/* Overlay with actions */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={(e) => handleDownload(image, e)}
                          className="bg-white/90 hover:bg-white text-black"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    {/* Image Info */}
                    <div className="p-3">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3 text-white" />
                        <span className="text-xs text-white">
                          {formatDate(image.processedAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Actions */}
              {images.length > 0 && (
                <div className="flex items-center justify-between pt-3 border-t border-accent/20">
                  <div className="text-xs text-white">
                    Click an image to restore it
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={refreshImages}
                      className="text-xs"
                    >
                      Refresh
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleClearAll}
                      className="text-xs text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Clear All
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}
